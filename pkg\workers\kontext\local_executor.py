"""
[二次开发] Kontext 本地模式工作流执行器
直接调用本地ComfyUI环境

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext工作流本地执行器，负责与ComfyUI API交互
- 维护者：开发团队
- 最后更新：2025-01-13
- 相关任务：Kontext图片生成工作流

技术说明：
- 复用Flux工作流的成熟轮询和图片获取机制
- 支持双重检查：队列状态 + 历史记录
- 适配Kontext特定节点：节点8(VAEDecode) + 节点191(PreviewImage)
"""

import json
import asyncio
import aiohttp
import base64
import os
import tempfile
import logging
from typing import Dict, Any, List, Optional
from .kontext_base_executor import BaseKontextExecutor
from pkg.core.workflow.manager_base import WorkflowResult


class LocalKontextExecutor(BaseKontextExecutor):
    def __init__(self, api_url: str = "http://localhost:8188", timeout: int = 180):
        self.api_url = api_url
        self.timeout = timeout
        self.session = None
        self.logger = logging.getLogger(__name__)
        self.current_workflow_data = None  # 初始化工作流数据
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建aiohttp会话"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def _check_comfyui_connection(self) -> bool:
        """检查ComfyUI服务连接状态"""
        try:
            session = await self._get_session()
            async with session.get(f"{self.api_url}/", timeout=aiohttp.ClientTimeout(total=5)) as response:
                return response.status == 200
        except Exception as e:
            self.logger.error(f"🔍 [DEBUG] ComfyUI连接检查失败: {e}")
            return False
    
    async def execute_workflow(self, prompt: str, query: Any, *args, **kwargs) -> WorkflowResult:
        try:
            self.logger.info(f"🔍 [DEBUG] LocalKontextExecutor.execute_workflow 开始")
            self.logger.info(f"🔍 [DEBUG] - 工作流文件: {prompt}")
            self.logger.info(f"🔍 [DEBUG] - 参数类型: {type(query)}")
            if isinstance(query, dict):
                self.logger.info(f"🔍 [DEBUG] - 参数键: {list(query.keys())}")
                if 'prompt' in query:
                    self.logger.info(f"🔍 [DEBUG] - 提示词: {query['prompt']}")
                if 'images' in query:
                    images = query['images']
                    self.logger.info(f"🔍 [DEBUG] - 图片数量: {len(images)}")
                    for i, img in enumerate(images):
                        self.logger.info(f"🔍 [DEBUG] - 图片{i+1}: {len(img)} bytes")
            else:
                # 避免输出二进制数据，只显示类型和基本信息
                query_type = str(type(query))
                self.logger.info(f"🔍 [DEBUG] - 参数类型: {query_type}")

                # 安全地检查对象属性，避免输出二进制数据
                if hasattr(query, '__dict__'):
                    attrs = []
                    for key, value in query.__dict__.items():
                        if isinstance(value, bytes):
                            attrs.append(f"{key}: {len(value)} bytes")
                        elif isinstance(value, list) and value and isinstance(value[0], bytes):
                            attrs.append(f"{key}: {len(value)} images")
                        elif isinstance(value, str) and len(value) > 50:
                            attrs.append(f"{key}: '{value[:50]}...'")
                        else:
                            attrs.append(f"{key}: {value}")
                    self.logger.info(f"🔍 [DEBUG] - 参数属性: {attrs}")
                else:
                    self.logger.info(f"🔍 [DEBUG] - 参数内容: 无法安全显示")

            # 0. 预检查ComfyUI服务连接
            self.logger.info(f"🔍 [DEBUG] 检查ComfyUI服务连接状态")
            if not await self._check_comfyui_connection():
                error_msg = f'ComfyUI服务未启动或无法连接 ({self.api_url})'
                self.logger.error(f"🔍 [DEBUG] 错误: {error_msg}")
                return WorkflowResult(success=False, error_message=error_msg)

            # 1. 加载工作流文件
            workflow_data = await self._load_workflow_file(prompt)  # prompt作为workflow_file
            if not workflow_data:
                error_msg = f'工作流文件不存在: {prompt}'
                self.logger.error(f"🔍 [DEBUG] 错误: {error_msg}")
                return WorkflowResult(success=False, error_message=error_msg)

            self.logger.info(f"🔍 [DEBUG] 工作流文件加载成功，节点数: {len(workflow_data)}")

            # 保存工作流数据供后续使用
            self.current_workflow_data = workflow_data

            # 2. 更新工作流参数
            updated_workflow = self._update_workflow_params(workflow_data, query)
            self.logger.info(f"🔍 [DEBUG] 工作流参数更新完成")

            # 3. 上传图片（如果有）
            if 'images' in query and query['images']:
                self.logger.info(f"🔍 [DEBUG] 开始上传 {len(query['images'])} 张图片")
                updated_workflow = await self._upload_images_to_workflow(updated_workflow, query['images'])
                self.logger.info(f"🔍 [DEBUG] 图片上传完成")
            else:
                self.logger.info(f"🔍 [DEBUG] 无图片需要上传")

            # 4. 提交工作流到ComfyUI
            self.logger.info(f"🔍 [DEBUG] 开始提交工作流到ComfyUI")
            session = await self._get_session()
            prompt_id = await self._submit_workflow(session, updated_workflow)
            if not prompt_id:
                error_msg = '提交工作流失败'
                self.logger.error(f"🔍 [DEBUG] 错误: {error_msg}")
                return WorkflowResult(success=False, error_message=error_msg)

            self.logger.info(f"🔍 [DEBUG] 工作流提交成功，prompt_id: {prompt_id}")

            # 5. 等待执行完成 - 增强调试版本
            self.logger.info(f"🔍 [DEBUG] 开始等待工作流执行完成，prompt_id: {prompt_id}")

            # 🔥 立即检查一次ComfyUI状态
            session = await self._get_session()
            try:
                async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                    self.logger.info(f"🔍 [DEBUG] 立即检查历史记录状态: {response.status}")
                    if response.status == 200:
                        result = await response.json()
                        self.logger.info(f"🔍 [DEBUG] 历史记录中的prompt_id列表: {list(result.keys())}")
                        if prompt_id in result:
                            self.logger.info(f"🔍 [DEBUG] 找到目标prompt_id: {prompt_id}")
                        else:
                            self.logger.warning(f"🔍 [DEBUG] 未找到目标prompt_id: {prompt_id}")
            except Exception as e:
                self.logger.error(f"🔍 [DEBUG] 立即检查失败: {e}")

            image_data = await self._wait_for_completion_flux_style(prompt_id)
            if not image_data:
                # 🔍 诊断：如果主流程失败，运行独立测试
                self.logger.error(f"🔍 [DEBUG] 主流程获取图片失败，开始独立诊断测试")
                test_result = await self.test_image_download(prompt_id)
                self.logger.info(f"🔍 [DEBUG] 独立测试结果: {test_result}")

                error_msg = '工作流执行超时或失败'
                self.logger.error(f"🔍 [DEBUG] 错误: {error_msg}")
                return WorkflowResult(success=False, error_message=error_msg)

            self.logger.info(f"🔍 [DEBUG] 工作流执行成功，图片大小: {len(image_data)} bytes")

            return WorkflowResult(success=True, image_data=image_data, metadata={
                'prompt_id': prompt_id,
                'workflow_file': prompt
            })
            
        except Exception as e:
            error_msg = f'执行工作流失败: {str(e)}'
            self.logger.error(f"🔍 [DEBUG] 异常: {error_msg}")
            self.logger.error(f"🔍 [DEBUG] 异常类型: {type(e).__name__}")
            import traceback
            self.logger.error(f"🔍 [DEBUG] 异常堆栈: {traceback.format_exc()}")
            return WorkflowResult(success=False, error_message=error_msg)
    
    async def _load_workflow_file(self, workflow_file: str) -> Optional[Dict[str, Any]]:
        """加载工作流文件"""
        try:
            # 检查是否是完整路径
            if os.path.exists(workflow_file):
                file_path = workflow_file
            else:
                # 假设是相对于workflows目录的路径
                file_path = os.path.join('workflows', workflow_file)
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            self.logger.error(f"加载工作流文件失败: {e}")
            return None
    
    def _update_workflow_params(self, workflow: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        [重构] 更新工作流参数
        此函数已重构，旨在更健壮地将输入参数映射到ComfyUI工作流节点。
        不再硬编码依赖特定节点ID，而是通过class_type和meta.title进行匹配。
        """
        self.logger.info("🔍 [DEBUG] [重构] 开始更新工作流参数...")
        updated_workflow = workflow.copy()
        updated_count = 0

        # --- 提示词更新 ---
        if 'prompt' in params and params['prompt']:
            self.logger.info(f"🔍 [DEBUG] 准备更新主提示词: '{params['prompt'][:50]}...'")
            prompt_updated = False
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "CLIPTextEncode":
                    title = node_data.get("_meta", {}).get("title", "").lower()
                    self.logger.info(f"  - 检查节点 {node_id} (CLIPTextEncode), 标题: '{title}'")
                    
                    # 优先匹配 kontext_local_1image.json 中的 "prompt_input_01"
                    if title == "prompt_input_01":
                        node_data["inputs"]["text"] = params['prompt']
                        self.logger.info(f"    ✅ 更新了主提示词 (节点 {node_id}, 标题: '{title}')")
                        updated_count += 1
                        prompt_updated = True
                        break # 找到并更新后即可退出循环
                    # 兼容其他可能的主提示词标题
                    elif "positive" in title or "main prompt" in title or "prompt" == title:
                        node_data["inputs"]["text"] = params['prompt']
                        self.logger.info(f"    ✅ 更新了主提示词 (节点 {node_id}, 标题: '{title}')")
                        updated_count += 1
                        prompt_updated = True
                        break # 找到并更新后即可退出循环
            if not prompt_updated:
                self.logger.warning("⚠️ [警告] 未找到匹配的CLIPTextEncode节点来更新主提示词。")

        # --- 负面提示词更新 ---
        if 'negative_prompt' in params and params['negative_prompt']:
            self.logger.info(f"🔍 [DEBUG] 准备更新负面提示词: '{params['negative_prompt'][:50]}...'")
            negative_prompt_updated = False
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "CLIPTextEncode":
                    title = node_data.get("_meta", {}).get("title", "").lower()
                    self.logger.info(f"  - 检查节点 {node_id} (CLIPTextEncode), 标题: '{title}'")
                    # 匹配负面提示词标题
                    if "negative" in title or "negative prompt" in title:
                        node_data["inputs"]["text"] = params['negative_prompt']
                        self.logger.info(f"    ✅ 更新了负面提示词 (节点 {node_id}, 标题: '{title}')")
                        updated_count += 1
                        negative_prompt_updated = True
                        break # 找到并更新后即可退出循环
            if not negative_prompt_updated:
                self.logger.warning("⚠️ [警告] 未找到匹配的CLIPTextEncode节点来更新负面提示词。")
                self.logger.warning("    请注意：当前工作流可能通过其他方式处理负面提示词，或没有直接的文本输入节点。")


        # --- KSampler 参数更新 (steps, seed) ---
        # 注意：在Flux工作流中，KSampler的cfg应该保持为1，guidance由FluxGuidance节点控制
        ksampler_params_to_update = {
            "steps": "steps",
            "seed": "seed"
        }
        ksampler_updated_any = False
        if any(p in params for p in ksampler_params_to_update):
            self.logger.info(f"🔍 [DEBUG] 准备更新 KSampler 参数 (steps, seed)")
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "KSampler":
                    self.logger.info(f"  - 检查节点 {node_id} (KSampler)")
                    for param_key, input_key in ksampler_params_to_update.items():
                        if param_key in params:
                            node_data["inputs"][input_key] = params[param_key]
                            self.logger.info(f"    ✅ 更新了 {param_key} ({input_key}): {params[param_key]}")
                            updated_count += 1
                            ksampler_updated_any = True
                    # Assuming only one KSampler needs to be updated for these params
                    break 
            if not ksampler_updated_any:
                self.logger.warning("⚠️ [警告] 未找到匹配的KSampler节点来更新生成参数。")

        # --- FluxGuidance 参数更新 (guidance) ---
        if 'guidance' in params:
            self.logger.info(f"🔍 [DEBUG] 准备更新 FluxGuidance 参数: guidance={params['guidance']}")
            flux_guidance_updated = False
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "FluxGuidance":
                    self.logger.info(f"  - 检查节点 {node_id} (FluxGuidance)")
                    node_data["inputs"]["guidance"] = params['guidance']
                    self.logger.info(f"    ✅ 更新了 guidance: {params['guidance']}")
                    updated_count += 1
                    flux_guidance_updated = True
                    break  # 通常只有一个FluxGuidance节点
            if not flux_guidance_updated:
                self.logger.warning("⚠️ [警告] 未找到匹配的FluxGuidance节点来更新guidance参数。")

        # --- FluxKontextProImageNode 参数更新 (prompt, aspect_ratio, guidance, steps, seed) ---
        flux_kontext_pro_node_params_to_update = {
            "prompt": "prompt",
            "aspect_ratio": "aspect_ratio",
            "guidance": "guidance",
            "steps": "steps",
            "seed": "seed"
        }
        flux_kontext_pro_node_found = False
        if any(p in params for p in flux_kontext_pro_node_params_to_update):
            self.logger.info(f"🔍 [DEBUG] 准备更新 FluxKontextProImageNode 参数")
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "FluxKontextProImageNode":
                    self.logger.info(f"  - 检查节点 {node_id} (FluxKontextProImageNode)")
                    flux_kontext_pro_node_found = True
                    for param_key, input_key in flux_kontext_pro_node_params_to_update.items():
                        if param_key in params:
                            node_data["inputs"][input_key] = params[param_key]
                            self.logger.info(f"    ✅ 更新了 {param_key} ({input_key}): {params[param_key]}")
                            updated_count += 1
                    break # Assuming only one FluxKontextProImageNode needs to be updated
            if not flux_kontext_pro_node_found:
                self.logger.info("ℹ️ [信息] 工作流中未找到FluxKontextProImageNode节点，跳过更新。")

        # --- 最终总结 ---
        if updated_count == 0:
            self.logger.warning("⚠️ [警告] [重构] 未更新任何工作流参数。将使用模板中的默认值。")
        else:
            self.logger.info(f"✅ [重构] 成功更新了 {updated_count} 个参数。")

        return updated_workflow
    
    async def _upload_images_to_workflow(self, workflow: Dict[str, Any], images: List[bytes]) -> Dict[str, Any]:
        """将图片上传到工作流"""
        try:
            session = await self._get_session()
            
            # 查找图片输入节点
            image_input_nodes = []
            for node_id, node_data in workflow.items():
                if node_data.get("class_type") in ["LoadImage", "easy loadImageBase64"]:
                    image_input_nodes.append((node_id, node_data))
            
            # 按标题排序，确保正确的映射顺序
            image_input_nodes.sort(key=lambda x: x[1].get("_meta", {}).get("title", ""))
            
            # 上传图片并更新工作流
            for i, (node_id, node_data) in enumerate(image_input_nodes):
                if i < len(images):
                    # 上传图片到ComfyUI
                    image_name = await self._upload_image(session, images[i], f"kontext_input_{i+1}")
                    if image_name:
                        if node_data.get("class_type") == "LoadImage":
                            node_data["inputs"]["image"] = image_name
                        elif node_data.get("class_type") == "easy loadImageBase64":
                            # 对于base64节点，需要将图片转换为base64
                            import base64
                            image_base64 = base64.b64encode(images[i]).decode('utf-8')
                            node_data["inputs"]["base64_data"] = image_base64
            
            return workflow
            
        except Exception as e:
            self.logger.error(f"上传图片失败: {e}")
            return workflow
    
    async def _upload_image(self, session: aiohttp.ClientSession, image_data: bytes, filename: str) -> Optional[str]:
        """上传单张图片到ComfyUI"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name
            
            try:
                # 上传文件
                with open(temp_file_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('image', f, filename=filename)
                    
                    async with session.post(f"{self.api_url}/upload/image", data=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result.get('name', filename)
                        else:
                            self.logger.error(f"上传图片失败: {response.status}")
                            return None
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            self.logger.error(f"上传图片出错: {e}")
            return None
    
    async def _submit_workflow(self, session: aiohttp.ClientSession, workflow: Dict[str, Any]) -> Optional[str]:
        """提交工作流到ComfyUI"""
        try:
            async with session.post(
                f"{self.api_url}/prompt",
                json={"prompt": workflow},
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('prompt_id')
                else:
                    self.logger.error(f"提交工作流失败: {response.status}")
                    return None
        except Exception as e:
            self.logger.error(f"提交工作流出错: {e}")
            return None
    
    # 🗑️ 已删除冗余方法：_wait_for_completion (原始版本)
    
    # 🗑️ 已删除冗余方法：_download_image (原始版本)

    # �️ 已删除冗余方法：_find_final_image_output_node (原始版本)

    # 🗑️ 已删除冗余方法：_wait_for_completion_v2

    # 🗑️ 已删除冗余方法：_check_workflow_status_v2

    # 🗑️ 已删除冗余方法：_get_workflow_image_v2

    # 🗑️ 已删除冗余方法：_find_final_output_node_v2 和 _find_final_preview_node_v2

    # 🗑️ 已删除冗余方法：_download_image_from_node_v2

    # 🗑️ 已删除冗余方法：_wait_for_completion_simple (测试版本)

    async def _wait_for_completion_flux_style(self, prompt_id: str) -> Optional[bytes]:
        """
        基于Flux工作流的轮询实现 - 复用成熟的轮询机制
        """
        self.logger.info(f"🔍 [KONTEXT] 开始等待工作流完成: {prompt_id}")

        try:
            # 轮询检查状态
            max_polls = 180  # 最大轮询次数
            poll_interval = 1  # 轮询间隔(秒)

            session = await self._get_session()

            for poll_count in range(max_polls):
                # 检查队列状态
                queue_info = await self._check_queue_status_flux_style(session, prompt_id)
                if queue_info is None:
                    self.logger.error(f"🔍 [KONTEXT] 获取队列状态失败")
                    return None

                # 每10次轮询输出一次状态
                if poll_count % 10 == 0:
                    self.logger.info(f"🔍 [KONTEXT] 轮询第{poll_count}次，状态: {queue_info.get('status', 'unknown')}")

                # 检查是否完成
                if queue_info.get('completed', False):
                    self.logger.info(f"🔍 [KONTEXT] 工作流执行完成")

                    # 获取输出图片
                    image_data = await self._get_output_image_flux_style(session, prompt_id)
                    return image_data

                # 检查是否出错
                if queue_info.get('failed', False):
                    error_msg = queue_info.get('error', '未知错误')
                    self.logger.error(f"🔍 [KONTEXT] 工作流执行失败: {error_msg}")
                    return None

                # 等待下次轮询
                await asyncio.sleep(poll_interval)

            self.logger.error(f"🔍 [KONTEXT] 工作流执行超时，已轮询{max_polls}次")
            return None

        except Exception as e:
            self.logger.error(f"🔍 [KONTEXT] 等待工作流完成失败: {e}")
            import traceback
            self.logger.error(f"🔍 [KONTEXT] 异常堆栈: {traceback.format_exc()}")
            return None

    async def _check_queue_status_flux_style(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[Dict[str, Any]]:
        """
        检查队列状态 - 基于Flux的实现
        """
        try:
            # 1. 检查当前队列
            async with session.get(f"{self.api_url}/queue", timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    queue_data = await response.json()

                    # 检查是否在执行队列中
                    queue_running = queue_data.get('queue_running', [])
                    queue_pending = queue_data.get('queue_pending', [])

                    # 检查是否在运行中
                    for item in queue_running:
                        if item[1] == prompt_id:
                            return {'completed': False, 'failed': False, 'status': 'running'}

                    # 检查是否在等待中
                    for item in queue_pending:
                        if item[1] == prompt_id:
                            self.logger.info(f"🔍 [KONTEXT] 工作流在队列中等待: {prompt_id}")
                            return {'completed': False, 'failed': False, 'status': 'pending'}

            # 2. 检查历史记录（已完成或失败）
            async with session.get(f"{self.api_url}/history/{prompt_id}", timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    history_data = await response.json()

                    if prompt_id in history_data:
                        prompt_data = history_data[prompt_id]

                        # 检查是否有错误
                        if 'error' in prompt_data:
                            error_info = prompt_data['error']
                            error_msg = error_info.get('message', '未知错误') if isinstance(error_info, dict) else str(error_info)
                            self.logger.error(f"🔍 [KONTEXT] 工作流执行失败: {error_msg}")
                            return {
                                'completed': False,
                                'failed': True,
                                'error': error_msg,
                                'status': 'failed'
                            }

                        # 检查是否有输出（成功完成）
                        outputs = prompt_data.get('outputs', {})
                        if outputs:
                            self.logger.info(f"🔍 [KONTEXT] 工作流执行完成: {prompt_id}")
                            return {
                                'completed': True,
                                'failed': False,
                                'outputs': outputs,
                                'status': 'completed'
                            }
                        else:
                            # 在历史中但没有输出，可能还在处理
                            return {'completed': False, 'failed': False, 'status': 'processing'}
                    else:
                        # 不在历史中，可能还在队列中或刚提交
                        return {'completed': False, 'failed': False, 'status': 'unknown'}
                else:
                    self.logger.warning(f"🔍 [KONTEXT] 检查历史记录失败: {response.status}")
                    return None

        except Exception as e:
            self.logger.error(f"🔍 [KONTEXT] 检查队列状态异常: {e}")
            return None

    async def _get_output_image_flux_style(self, session: aiohttp.ClientSession, prompt_id: str) -> Optional[bytes]:
        """
        获取输出图片 - 基于Flux的实现，适配Kontext节点
        """
        try:
            self.logger.info(f"🔍 [KONTEXT] 开始获取输出图片，prompt_id: {prompt_id}")

            # 获取历史记录找到输出
            history_url = f"{self.api_url}/history/{prompt_id}"
            self.logger.info(f"🔍 [KONTEXT] 请求历史记录URL: {history_url}")

            async with session.get(history_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                self.logger.info(f"🔍 [KONTEXT] 历史记录响应状态: {response.status}")
                if response.status != 200:
                    self.logger.error(f"🔍 [KONTEXT] 获取历史记录失败: {response.status}")
                    return None

                history_data = await response.json()
                self.logger.info(f"🔍 [KONTEXT] 历史记录数据键: {list(history_data.keys())}")

                prompt_data = history_data.get(prompt_id, {})
                if not prompt_data:
                    self.logger.error(f"🔍 [KONTEXT] 历史记录中未找到prompt_id: {prompt_id}")
                    return None

                outputs = prompt_data.get('outputs', {})
                self.logger.info(f"🔍 [KONTEXT] 工作流输出节点: {list(outputs.keys())}")

                # 详细分析每个输出节点
                for node_id, node_output in outputs.items():
                    has_images = 'images' in node_output and node_output['images']
                    image_count = len(node_output.get('images', [])) if has_images else 0
                    self.logger.info(f"🔍 [KONTEXT] 节点{node_id}: 有图片={has_images}, 图片数={image_count}")
                    if has_images:
                        for i, img_info in enumerate(node_output['images']):
                            self.logger.info(f"🔍 [KONTEXT] 节点{node_id}图片{i}: {img_info}")

                # 策略1: 查找节点8 (VAEDecode, final_image_output)
                if '8' in outputs and 'images' in outputs['8'] and outputs['8']['images']:
                    self.logger.info(f"🔍 [KONTEXT] 策略1: 尝试从节点8获取图片")
                    image_data = await self._download_image_flux_style(session, outputs['8'], "节点8")
                    if image_data:
                        self.logger.info(f"🔍 [KONTEXT] 策略1成功: 从节点8获取图片 {len(image_data)} bytes")
                        return image_data
                    else:
                        self.logger.warning(f"🔍 [KONTEXT] 策略1失败: 节点8图片下载失败")

                # 策略2: 查找节点191 (PreviewImage, final_image)
                if '191' in outputs and 'images' in outputs['191'] and outputs['191']['images']:
                    self.logger.info(f"🔍 [KONTEXT] 策略2: 尝试从节点191获取图片")
                    image_data = await self._download_image_flux_style(session, outputs['191'], "节点191")
                    if image_data:
                        self.logger.info(f"🔍 [KONTEXT] 策略2成功: 从节点191获取图片 {len(image_data)} bytes")
                        return image_data
                    else:
                        self.logger.warning(f"🔍 [KONTEXT] 策略2失败: 节点191图片下载失败")

                # 策略3: 智能识别最终输出节点（使用工作流数据）
                final_node_id = self._identify_final_output_node_kontext(outputs)
                if final_node_id and final_node_id in outputs:
                    self.logger.info(f"🔍 [KONTEXT] 策略3: 尝试从智能识别的节点{final_node_id}获取图片")
                    image_data = await self._download_image_flux_style(session, outputs[final_node_id], f"智能节点{final_node_id}")
                    if image_data:
                        self.logger.info(f"🔍 [KONTEXT] 策略3成功: 从节点{final_node_id}获取图片 {len(image_data)} bytes")
                        return image_data
                    else:
                        self.logger.warning(f"🔍 [KONTEXT] 策略3失败: 节点{final_node_id}图片下载失败")

                self.logger.error(f"🔍 [KONTEXT] 所有策略都未能获取到图片")
                self.logger.error(f"🔍 [KONTEXT] 最终诊断 - 输出节点数: {len(outputs)}, 节点详情: {outputs}")
                return None

        except Exception as e:
            self.logger.error(f"🔍 [KONTEXT] 获取输出图片失败: {e}")
            import traceback
            self.logger.error(f"🔍 [KONTEXT] 异常堆栈: {traceback.format_exc()}")
            return None

    def _identify_final_output_node_kontext(self, outputs: Dict[str, Any]) -> Optional[str]:
        """
        智能识别Kontext工作流的最终输出节点
        """
        try:
            if not self.current_workflow_data:
                return None

            # 查找所有有图片输出的节点
            image_output_nodes = []
            for node_id, output_data in outputs.items():
                if 'images' in output_data and output_data['images']:
                    image_output_nodes.append(node_id)

            if not image_output_nodes:
                return None

            self.logger.info(f"🔍 [KONTEXT] 有图片输出的节点: {image_output_nodes}")

            # 优先级1: 查找title为final_image_output的VAEDecode节点
            for node_id in image_output_nodes:
                if node_id in self.current_workflow_data:
                    node_data = self.current_workflow_data[node_id]
                    title = node_data.get('_meta', {}).get('title', '')
                    class_type = node_data.get('class_type', '')

                    if title == 'final_image_output' and class_type == 'VAEDecode':
                        self.logger.info(f"🔍 [KONTEXT] 找到final_image_output节点: {node_id}")
                        return node_id

            # 优先级2: 查找title为final_image的PreviewImage节点
            for node_id in image_output_nodes:
                if node_id in self.current_workflow_data:
                    node_data = self.current_workflow_data[node_id]
                    title = node_data.get('_meta', {}).get('title', '')
                    class_type = node_data.get('class_type', '')

                    if title == 'final_image' and class_type == 'PreviewImage':
                        self.logger.info(f"🔍 [KONTEXT] 找到final_image节点: {node_id}")
                        return node_id

            # 优先级3: 选择第一个有效的输出节点
            if image_output_nodes:
                node_id = image_output_nodes[0]
                self.logger.info(f"🔍 [KONTEXT] 使用第一个输出节点: {node_id}")
                return node_id

            return None

        except Exception as e:
            self.logger.error(f"🔍 [KONTEXT] 识别最终输出节点失败: {e}")
            return None

    async def _download_image_flux_style(self, session: aiohttp.ClientSession, node_output: Dict[str, Any], source_desc: str = "未知节点") -> Optional[bytes]:
        """
        下载图片 - 基于Flux的实现，增强调试信息
        """
        try:
            self.logger.info(f"🔍 [KONTEXT] 开始下载图片，来源: {source_desc}")

            if 'images' not in node_output:
                self.logger.error(f"🔍 [KONTEXT] 节点输出中没有'images'字段: {node_output.keys()}")
                return None

            if not node_output['images']:
                self.logger.error(f"🔍 [KONTEXT] 节点输出中'images'字段为空")
                return None

            images = node_output['images']
            self.logger.info(f"🔍 [KONTEXT] 找到{len(images)}张图片")

            # 获取第一张图片
            image_info = images[0]
            self.logger.info(f"🔍 [KONTEXT] 第一张图片信息: {image_info}")

            filename = image_info['filename']
            subfolder = image_info.get('subfolder', '')
            image_type = image_info.get('type', '')

            self.logger.info(f"🔍 [KONTEXT] 图片参数 - filename: {filename}, subfolder: '{subfolder}', type: '{image_type}'")

            # 构建下载URL
            url_params = [f"filename={filename}"]
            if subfolder:
                url_params.append(f"subfolder={subfolder}")
            if image_type:
                url_params.append(f"type={image_type}")

            url = f"{self.api_url}/view?" + "&".join(url_params)
            self.logger.info(f"🔍 [KONTEXT] 构建的下载URL: {url}")

            async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                self.logger.info(f"🔍 [KONTEXT] 图片下载响应状态: {response.status}")
                self.logger.info(f"🔍 [KONTEXT] 响应头: {dict(response.headers)}")

                if response.status == 200:
                    image_data = await response.read()
                    data_size = len(image_data)
                    self.logger.info(f"🔍 [KONTEXT] 下载图片成功: {filename}, 大小: {data_size} bytes")

                    # 验证图片数据
                    if data_size == 0:
                        self.logger.error(f"🔍 [KONTEXT] 警告: 下载的图片数据为空")
                        return None

                    # 使用官方统一的图片格式检测
                    from pkg.core.image.utils import detect_image_type
                    detected_type = detect_image_type(image_data)
                    format_type = detected_type.upper() if detected_type != "unknown" else "未知格式"

                    self.logger.info(f"🔍 [KONTEXT] 图片格式检测: {format_type}")
                    self.logger.info(f"🔍 [KONTEXT] 图片数据前16字节: {image_data[:16].hex()}")

                    return image_data
                else:
                    self.logger.error(f"🔍 [KONTEXT] 下载图片失败: HTTP {response.status}")
                    response_text = await response.text()
                    self.logger.error(f"🔍 [KONTEXT] 错误响应内容: {response_text[:200]}")
                    return None

        except Exception as e:
            self.logger.error(f"🔍 [KONTEXT] 下载图片异常: {e}")
            import traceback
            self.logger.error(f"🔍 [KONTEXT] 下载异常堆栈: {traceback.format_exc()}")
            return None

    async def test_image_download(self, prompt_id: str) -> bool:
        """
        独立测试图片下载功能
        """
        try:
            self.logger.info(f"🧪 [TEST] 开始独立测试图片下载，prompt_id: {prompt_id}")

            session = await self._get_session()

            # 1. 测试历史记录获取
            history_url = f"{self.api_url}/history/{prompt_id}"
            async with session.get(history_url) as response:
                if response.status != 200:
                    self.logger.error(f"🧪 [TEST] 历史记录获取失败: {response.status}")
                    return False

                history_data = await response.json()
                prompt_data = history_data.get(prompt_id, {})
                outputs = prompt_data.get('outputs', {})

                self.logger.info(f"🧪 [TEST] 历史记录获取成功，输出节点: {list(outputs.keys())}")

            # 2. 测试直接访问ComfyUI输出目录
            try:
                async with session.get(f"{self.api_url}/view") as response:
                    self.logger.info(f"🧪 [TEST] ComfyUI /view 端点状态: {response.status}")
            except Exception as e:
                self.logger.error(f"🧪 [TEST] ComfyUI /view 端点测试失败: {e}")

            # 3. 如果有输出，尝试下载第一个可用图片
            for node_id, node_output in outputs.items():
                if 'images' in node_output and node_output['images']:
                    self.logger.info(f"🧪 [TEST] 测试下载节点{node_id}的图片")
                    image_data = await self._download_image_flux_style(session, node_output, f"测试节点{node_id}")
                    if image_data:
                        self.logger.info(f"🧪 [TEST] 测试成功: 下载了{len(image_data)}字节的图片数据")
                        return True
                    else:
                        self.logger.warning(f"🧪 [TEST] 测试失败: 节点{node_id}图片下载失败")

            self.logger.error(f"🧪 [TEST] 测试失败: 没有可下载的图片")
            return False

        except Exception as e:
            self.logger.error(f"🧪 [TEST] 测试异常: {e}")
            import traceback
            self.logger.error(f"🧪 [TEST] 测试异常堆栈: {traceback.format_exc()}")
            return False
